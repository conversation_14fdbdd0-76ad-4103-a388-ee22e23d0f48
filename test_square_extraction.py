#!/usr/bin/env python3
"""
测试正方形分割功能的独立脚本
"""

import numpy as np
import cv2
from itertools import combinations

def calculate_vertex_angle(p1, p2, p3):
    """
    计算三个点组成的角度（以p2为顶点）
    返回角度值（度数）
    """
    try:
        # 创建向量
        v1 = np.array([p1[0] - p2[0], p1[1] - p2[1]], dtype=np.float64)
        v2 = np.array([p3[0] - p2[0], p3[1] - p2[1]], dtype=np.float64)
        
        # 计算向量长度
        norm_v1 = np.linalg.norm(v1)
        norm_v2 = np.linalg.norm(v2)
        
        if norm_v1 == 0 or norm_v2 == 0:
            return 0.0
        
        # 计算夹角余弦值
        cos_angle = np.dot(v1, v2) / (norm_v1 * norm_v2)
        
        # 限制余弦值在[-1, 1]范围内，避免数值误差
        cos_angle = np.clip(cos_angle, -1.0, 1.0)
        
        # 计算角度（弧度转度数）
        angle = np.arccos(cos_angle)
        angle_degrees = np.degrees(angle)
        
        return angle_degrees
    except Exception as e:
        print(f"计算角度时出错: {e}")
        return 0.0

def is_right_angle(angle, tolerance=10.0):
    """判断角度是否接近90度"""
    return abs(angle - 90.0) <= tolerance

def calculate_edge_length(p1, p2):
    """计算两点之间的距离"""
    return np.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)

def are_edges_equal(length1, length2, tolerance=0.15):
    """判断两条边长是否相等（允许15%的误差）"""
    if length1 == 0 or length2 == 0:
        return False
    ratio = min(length1, length2) / max(length1, length2)
    return ratio >= (1.0 - tolerance)

def validate_square_geometry(vertices):
    """
    验证四个顶点是否构成正方形
    返回 (is_valid, confidence_score, info)
    """
    if len(vertices) != 4:
        return False, 0.0, "顶点数量不是4个"
    
    try:
        # 计算所有边长
        edges = []
        for i in range(4):
            p1 = vertices[i]
            p2 = vertices[(i + 1) % 4]
            edge_length = calculate_edge_length(p1, p2)
            edges.append(edge_length)
        
        # 计算所有角度
        angles = []
        for i in range(4):
            p1 = vertices[(i - 1) % 4]
            p2 = vertices[i]
            p3 = vertices[(i + 1) % 4]
            angle = calculate_vertex_angle(p1, p2, p3)
            angles.append(angle)
        
        # 验证边长相等性
        edge_scores = []
        for i in range(4):
            for j in range(i + 1, 4):
                if are_edges_equal(edges[i], edges[j]):
                    edge_scores.append(1.0)
                else:
                    ratio = min(edges[i], edges[j]) / max(edges[i], edges[j])
                    edge_scores.append(ratio)
        
        # 验证角度接近90度
        angle_scores = []
        for angle in angles:
            if is_right_angle(angle):
                angle_scores.append(1.0)
            else:
                # 角度偏差越小，分数越高
                deviation = abs(angle - 90.0)
                score = max(0.0, 1.0 - deviation / 45.0)  # 45度偏差时分数为0
                angle_scores.append(score)
        
        # 计算对角线长度
        diag1 = calculate_edge_length(vertices[0], vertices[2])
        diag2 = calculate_edge_length(vertices[1], vertices[3])
        diagonal_score = 1.0 if are_edges_equal(diag1, diag2, 0.2) else min(diag1, diag2) / max(diag1, diag2)
        
        # 综合评分
        edge_score = np.mean(edge_scores)
        angle_score = np.mean(angle_scores)
        
        overall_score = (edge_score * 0.4 + angle_score * 0.4 + diagonal_score * 0.2)
        
        # 判断是否为有效正方形
        is_valid = (edge_score >= 0.8 and angle_score >= 0.7 and diagonal_score >= 0.7)
        
        info = f"边长分数:{edge_score:.2f}, 角度分数:{angle_score:.2f}, 对角线分数:{diagonal_score:.2f}"
        
        return is_valid, overall_score, info
        
    except Exception as e:
        return False, 0.0, f"验证时出错: {e}"

def find_square_candidates_from_vertices(vertices, min_square_size=10):
    """
    从顶点列表中寻找可能的正方形组合
    """
    candidates = []
    n = len(vertices)
    
    if n < 4:
        return candidates
    
    print(f"  🔍 从 {n} 个顶点中寻找正方形组合...")
    
    # 尝试所有4个顶点的组合
    for combo in combinations(range(n), 4):
        square_vertices = [vertices[i] for i in combo]
        
        # 验证这4个点是否构成正方形
        is_valid, score, info = validate_square_geometry(square_vertices)
        
        if is_valid:
            # 计算正方形的边长（用于过滤太小的正方形）
            edge_length = calculate_edge_length(square_vertices[0], square_vertices[1])
            
            if edge_length >= min_square_size:
                candidates.append({
                    'vertices': square_vertices,
                    'score': score,
                    'edge_length': edge_length,
                    'info': info,
                    'indices': combo
                })
                print(f"    ✅ 找到正方形候选: 边长={edge_length:.1f}, 分数={score:.2f}")
    
    # 按分数排序
    candidates.sort(key=lambda x: x['score'], reverse=True)
    
    return candidates

def extract_squares_from_polygon(polygon_vertices, min_square_size=15, max_squares=10):
    """
    从多边形顶点中提取正方形
    """
    print(f"\n🔍 开始从多边形中提取正方形...")
    print(f"  输入顶点数: {len(polygon_vertices)}")
    print(f"  最小边长: {min_square_size}")
    
    if len(polygon_vertices) < 4:
        print("  ❌ 顶点数量不足，无法构成正方形")
        return []
    
    # 第一步：寻找正方形候选
    candidates = find_square_candidates_from_vertices(polygon_vertices, min_square_size)
    
    if not candidates:
        print("  ❌ 未找到有效的正方形候选")
        return []
    
    print(f"  📋 找到 {len(candidates)} 个正方形候选")
    
    # 第三步：限制数量并返回结果
    final_squares = candidates[:max_squares]
    
    print(f"  ✅ 最终提取 {len(final_squares)} 个正方形")
    
    return final_squares

def test_square_extraction():
    """测试正方形分割功能"""
    print("=" * 60)
    print("测试正方形分割功能")
    print("=" * 60)
    
    # 测试用例1：完美的正方形
    print("\n测试用例1：完美的正方形")
    perfect_square = [(0, 0), (100, 0), (100, 100), (0, 100)]
    result1 = extract_squares_from_polygon(perfect_square)
    print(f"结果：提取了 {len(result1)} 个正方形")
    
    # 测试用例2：包含两个重叠正方形的复杂多边形
    print("\n测试用例2：包含两个重叠正方形的复杂多边形")
    complex_polygon = [
        (0, 0), (50, 0), (100, 0), (100, 50), (100, 100), 
        (50, 100), (0, 100), (0, 50), (50, 50), (50, 25), (25, 25), (25, 75)
    ]
    result2 = extract_squares_from_polygon(complex_polygon)
    print(f"结果：提取了 {len(result2)} 个正方形")
    
    # 测试用例3：旋转的正方形
    print("\n测试用例3：旋转的正方形")
    import math
    angle = math.pi / 4  # 45度
    cos_a, sin_a = math.cos(angle), math.sin(angle)
    center = (50, 50)
    size = 30
    rotated_square = []
    for dx, dy in [(-size, -size), (size, -size), (size, size), (-size, size)]:
        x = center[0] + dx * cos_a - dy * sin_a
        y = center[1] + dx * sin_a + dy * cos_a
        rotated_square.append((x, y))
    
    result3 = extract_squares_from_polygon(rotated_square)
    print(f"结果：提取了 {len(result3)} 个正方形")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == "__main__":
    test_square_extraction()
